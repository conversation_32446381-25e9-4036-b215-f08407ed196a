using UnrealBuildTool;

public class SageBETUtilities : ModuleRules
{
    public SageBETUtilities(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDefinitions.Add("SAGEBETUTILITIES_API=DLLEXPORT");

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "InputCore",
                "Json",
            }
        );

        // Add editor-specific dependencies only when building for editor
        if (Target.bBuildEditor == true)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "UnrealEd",
                    "AssetRegistry",
                }
            );
        }

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",
                
                "SageJSONUtilities",
                
                "Black_Eye"
            });
    }
}
