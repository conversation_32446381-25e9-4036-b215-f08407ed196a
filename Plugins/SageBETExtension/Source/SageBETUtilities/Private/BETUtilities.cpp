#include "BETUtilities.h"
#include "LogDruids.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"

TArray<FBlackEyeTarget> UBETUtilities::GetLookAtSubjectsArray(TSharedPtr<ULookAtComponent>& LookAtComponent)
{
	TArray<FBlackEyeTarget> SubjectsArray;

	for (int i = 0; i < 15; i++)
	{
		FBlackEyeTarget BlackEyeTarget;
		LookAtComponent->GetTargetAtIndex(BlackEyeTarget, i);
		SubjectsArray.Add(BlackEyeTarget);
	}

	return SubjectsArray;
}

void UBETUtilities::SetLookAtSubjectParams(TSharedPtr<ULookAtComponent>& LookAtComponent, FBlackEyeTarget SubjectParams,
	int32 SubjectIndex)
{
	if (!LookAtComponent.IsValid())
	{
		return;
	}
	
	if (SubjectIndex == 0)
	{
		int NumTargets = LookAtComponent->GetNumTargets();
		for (int i = 0; i < NumTargets; i++)
		{
			FBlackEyeTarget BlackEyeTarget;
			LookAtComponent->GetTargetAtIndex(BlackEyeTarget, i);
			if (!BlackEyeTarget.Actor.IsValid())
			{
				LookAtComponent->SetLookAt(SubjectParams, i, false);
				return;
			}
		}

		return;
	}

	LookAtComponent->SetLookAt(SubjectParams, SubjectIndex - 1, false);
}
